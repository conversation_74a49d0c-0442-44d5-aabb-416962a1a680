<?php
/**
 * Test sayfası - Tutor LMS ürün filtreleme testi
 * Bu dosyayı WordPress admin panelinde çalıştırarak test edebilirsiniz
 */

// WordPress'e doğrudan erişim<PERSON> engelle
if (!defined('ABSPATH')) {
    exit;
}

// Test fonksiyonu
function test_tutor_product_filtering() {
    echo '<div class="wrap">';
    echo '<h1>Tutor LMS Ürün Filtreleme Testi</h1>';
    
    // Mevcut kullanıcı bilgilerini göster
    $current_user = wp_get_current_user();
    echo '<h2>Mevcut Kullanıcı Bilgileri</h2>';
    echo '<p><strong>Kullanıcı ID:</strong> ' . $current_user->ID . '</p>';
    echo '<p><strong>Kullanıcı Adı:</strong> ' . $current_user->user_login . '</p>';
    echo '<p><strong>Roller:</strong> ' . implode(', ', $current_user->roles) . '</p>';
    
    // Role Custom eklentisinin aktif olup olmadığını kontrol et
    if (class_exists('Role_Custom')) {
        echo '<p style="color: green;"><strong>✓ Role Custom eklentisi aktif</strong></p>';
        
        $role_custom = Role_Custom::get_instance();
        $is_tutor_instructor = in_array('tutor_instructor', $current_user->roles);
        $is_admin = in_array('administrator', $current_user->roles);
        
        echo '<p><strong>Tutor Instructor mı:</strong> ' . ($is_tutor_instructor ? 'Evet' : 'Hayır') . '</p>';
        echo '<p><strong>Admin mi:</strong> ' . ($is_admin ? 'Evet' : 'Hayır') . '</p>';
        
        // Kullanıcının ürünlerini test et
        if ($is_tutor_instructor && !$is_admin) {
            echo '<h2>Kullanıcının Ürünleri (Role Custom Filtreleme)</h2>';
            
            // Reflection kullanarak private metoda erişim
            $reflection = new ReflectionClass($role_custom);
            $get_user_products_method = $reflection->getMethod('get_user_products');
            $get_user_products_method->setAccessible(true);
            $user_products = $get_user_products_method->invoke($role_custom);
            
            if (empty($user_products)) {
                echo '<p style="color: orange;">Bu kullanıcının hiç ürünü yok.</p>';
            } else {
                echo '<ul>';
                foreach ($user_products as $product_id) {
                    $product = get_post($product_id);
                    if ($product) {
                        echo '<li>ID: ' . $product_id . ' - ' . $product->post_title . '</li>';
                    }
                }
                echo '</ul>';
            }
        } else {
            echo '<p style="color: blue;">Bu test sadece tutor_instructor rolündeki kullanıcılar için çalışır.</p>';
        }
        
    } else {
        echo '<p style="color: red;"><strong>✗ Role Custom eklentisi aktif değil</strong></p>';
    }
    
    // Tutor LMS'in aktif olup olmadığını kontrol et
    if (class_exists('TUTOR\Tutor')) {
        echo '<p style="color: green;"><strong>✓ Tutor LMS aktif</strong></p>';
        
        // Tüm ürünleri göster (filtrelenmemiş)
        echo '<h2>Tüm WooCommerce Ürünleri (Filtrelenmemiş)</h2>';
        if (function_exists('tutor_utils')) {
            $all_products = tutor_utils()->get_wc_products_db();
            if (empty($all_products)) {
                echo '<p>Hiç WooCommerce ürünü bulunamadı.</p>';
            } else {
                echo '<ul>';
                foreach ($all_products as $product) {
                    echo '<li>ID: ' . $product->ID . ' - ' . $product->post_title . '</li>';
                }
                echo '</ul>';
            }
        }
        
    } else {
        echo '<p style="color: red;"><strong>✗ Tutor LMS aktif değil</strong></p>';
    }
    
    // WooCommerce kontrolü
    if (class_exists('WooCommerce')) {
        echo '<p style="color: green;"><strong>✓ WooCommerce aktif</strong></p>';
    } else {
        echo '<p style="color: red;"><strong>✗ WooCommerce aktif değil</strong></p>';
    }
    
    echo '</div>';
}

// Admin sayfasında test çalıştır
if (is_admin() && isset($_GET['test_tutor_products'])) {
    add_action('admin_notices', 'test_tutor_product_filtering');
}

// Test linkini admin menüsüne ekle
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Tutor Ürün Filtreleme Testi',
            'Tutor Ürün Testi',
            'manage_options',
            'test-tutor-products',
            function() {
                echo '<div class="wrap">';
                echo '<h1>Tutor LMS Ürün Filtreleme Testi</h1>';
                echo '<p><a href="' . admin_url('tools.php?page=test-tutor-products&test_tutor_products=1') . '" class="button button-primary">Testi Çalıştır</a></p>';
                
                if (isset($_GET['test_tutor_products'])) {
                    test_tutor_product_filtering();
                }
                
                echo '</div>';
            }
        );
    }
});
