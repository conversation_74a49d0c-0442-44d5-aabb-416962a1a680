/**
 * Tutor LMS Ürün Filtreleme Test JavaScript
 * Bu dosya Tutor LMS kurs oluşturucusundaki ürün seçme alanının 
 * doğru filtrelenip filtrelenmediğini test eder
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Test butonu ekle
    function addTestButton() {
        // Sadece Tutor LMS kurs oluşturma/düzenleme sayfalarında çalış
        if (window.location.href.indexOf('tutor') === -1 && 
            window.location.href.indexOf('course') === -1) {
            return;
        }
        
        // Test butonu HTML'i
        var testButtonHtml = `
            <div id="role-custom-tutor-test" style="
                position: fixed; 
                top: 50px; 
                right: 20px; 
                z-index: 9999; 
                background: #0073aa; 
                color: white; 
                padding: 10px; 
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                font-size: 12px;
                max-width: 300px;
            ">
                <h4 style="margin: 0 0 10px 0; color: white;">Role Custom Test</h4>
                <button id="test-tutor-products-ajax" class="button button-small" style="margin-right: 5px;">
                    Test AJAX
                </button>
                <button id="test-product-dropdown" class="button button-small">
                    Test Dropdown
                </button>
                <div id="test-results" style="margin-top: 10px; font-size: 11px;"></div>
            </div>
        `;
        
        $('body').append(testButtonHtml);
        
        // Test butonları için event listener'lar
        $('#test-tutor-products-ajax').on('click', testTutorProductsAjax);
        $('#test-product-dropdown').on('click', testProductDropdown);
    }
    
    // Tutor LMS AJAX çağrısını test et
    function testTutorProductsAjax() {
        $('#test-results').html('<div style="color: yellow;">AJAX testi çalışıyor...</div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_get_wc_products',
                exclude_linked_products: true,
                course_id: 0,
                _wpnonce: $('meta[name="csrf-token"]').attr('content') || ''
            },
            success: function(response) {
                console.log('Tutor AJAX Response:', response);
                
                var resultHtml = '<div style="color: lightgreen;">✓ AJAX başarılı</div>';
                
                if (response.success && response.data) {
                    var products = response.data.data || response.data;
                    resultHtml += '<div>Ürün sayısı: ' + (Array.isArray(products) ? products.length : 0) + '</div>';
                    
                    if (Array.isArray(products) && products.length > 0) {
                        resultHtml += '<div style="max-height: 100px; overflow-y: auto; margin-top: 5px;">';
                        products.slice(0, 3).forEach(function(product) {
                            resultHtml += '<div style="font-size: 10px;">• ' + (product.post_title || product.title || 'ID: ' + product.ID) + '</div>';
                        });
                        if (products.length > 3) {
                            resultHtml += '<div style="font-size: 10px;">... ve ' + (products.length - 3) + ' tane daha</div>';
                        }
                        resultHtml += '</div>';
                    }
                } else {
                    resultHtml += '<div style="color: orange;">Ürün bulunamadı</div>';
                }
                
                $('#test-results').html(resultHtml);
            },
            error: function(xhr, status, error) {
                console.error('Tutor AJAX Error:', error, xhr.responseText);
                $('#test-results').html('<div style="color: red;">✗ AJAX hatası: ' + error + '</div>');
            }
        });
    }
    
    // Ürün dropdown'ını test et
    function testProductDropdown() {
        $('#test-results').html('<div style="color: yellow;">Dropdown testi çalışıyor...</div>');
        
        // Ürün seçme alanını bul
        var productSelectors = [
            'select[name*="product"]',
            'select[id*="product"]',
            '.wc-product-search',
            '.tutor-product-select',
            'select.wc-enhanced-select',
            '[data-action="woocommerce_json_search_products_and_variations"]'
        ];
        
        var foundSelectors = [];
        var totalOptions = 0;
        
        productSelectors.forEach(function(selector) {
            var elements = $(selector);
            if (elements.length > 0) {
                elements.each(function() {
                    var $this = $(this);
                    var optionCount = $this.find('option').length;
                    foundSelectors.push({
                        selector: selector,
                        element: $this,
                        options: optionCount,
                        id: $this.attr('id') || 'no-id',
                        name: $this.attr('name') || 'no-name'
                    });
                    totalOptions += optionCount;
                });
            }
        });
        
        var resultHtml = '';
        if (foundSelectors.length > 0) {
            resultHtml += '<div style="color: lightgreen;">✓ ' + foundSelectors.length + ' dropdown bulundu</div>';
            foundSelectors.forEach(function(item) {
                resultHtml += '<div style="font-size: 10px;">• ' + item.selector + ' (' + item.options + ' seçenek)</div>';
            });
        } else {
            resultHtml += '<div style="color: orange;">Ürün dropdown\'ı bulunamadı</div>';
            resultHtml += '<div style="font-size: 10px;">Sayfada bulunan select elementleri:</div>';
            $('select').each(function(index) {
                if (index < 5) { // Sadece ilk 5 tanesini göster
                    var $this = $(this);
                    resultHtml += '<div style="font-size: 9px;">• ' + ($this.attr('id') || $this.attr('name') || 'select-' + index) + '</div>';
                }
            });
        }
        
        $('#test-results').html(resultHtml);
    }
    
    // CSS selector'ı test et
    function testCSSSelector() {
        var targetSelector = 'body > div.css-he4l7v > div > div.css-pqslv2';
        var elements = $(targetSelector);
        
        console.log('CSS Selector Test:', targetSelector, 'Found:', elements.length);
        
        if (elements.length > 0) {
            elements.each(function() {
                console.log('Found element:', this);
            });
        }
        
        // Alternatif selector'ları da test et
        var alternativeSelectors = [
            '.css-he4l7v',
            '.css-pqslv2',
            '[class*="css-he4l7v"]',
            '[class*="css-pqslv2"]'
        ];
        
        alternativeSelectors.forEach(function(selector) {
            var found = $(selector);
            if (found.length > 0) {
                console.log('Alternative selector found:', selector, found.length);
            }
        });
    }
    
    // Sayfa yüklendiğinde test butonunu ekle
    setTimeout(addTestButton, 2000);
    
    // CSS selector testini çalıştır
    setTimeout(testCSSSelector, 3000);
    
    // Tutor LMS frontend builder'ı izle
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                // Yeni eklenen node'ları kontrol et
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        var $node = $(node);
                        
                        // Ürün seçme alanı eklenmiş mi kontrol et
                        if ($node.find('select[name*="product"], .wc-product-search').length > 0) {
                            console.log('Product selector added to DOM:', node);
                        }
                        
                        // CSS class'ları kontrol et
                        if ($node.hasClass('css-he4l7v') || $node.hasClass('css-pqslv2')) {
                            console.log('Target CSS class found:', node);
                        }
                    }
                });
            }
        });
    });
    
    // DOM değişikliklerini izlemeye başla
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Global test fonksiyonları
    window.roleCustomTutorTest = {
        testAjax: testTutorProductsAjax,
        testDropdown: testProductDropdown,
        testCSS: testCSSSelector
    };
});
